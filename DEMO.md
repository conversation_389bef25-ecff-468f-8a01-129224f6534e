# Align 应用演示

## 认证系统演示

### 1. 应用启动流程

当应用启动时，会经历以下步骤：

```
应用启动 → AuthWrapper检查登录状态 → 根据状态显示页面
```

- **未登录**: SetupPage（引导页）→ LoginPage（登录页）
- **已登录**: 直接显示MainPage（主应用）

### 2. 登录演示

1. **启动应用**
   ```bash
   flutter run
   ```

2. **首次使用**
   - 看到"Align"启动页面
   - 点击任意位置进入登录页面

3. **登录操作**
   - 选择"验证码登录"或"密码登录"
   - 勾选"同意用户协议和隐私政策"
   - 点击"登录"按钮
   - 系统会模拟登录过程（1秒延迟）
   - 登录成功后自动跳转到主应用

### 3. 主应用界面

登录成功后，您会看到：
- 底部导航栏（4个标签页）
- 聊天、日历、发现、个人资料页面

### 4. 登出演示

1. **进入个人资料页面**
   - 点击底部导航栏最右侧的标签
   - 进入用户个人资料页面

2. **执行登出**
   - 点击红色的"登出"按钮
   - 确认登出操作
   - 系统会自动跳转回启动页面

### 5. 状态管理验证

整个过程中，您可以观察到：
- 登录状态的实时更新
- 页面的自动跳转
- 无需手动管理路由

## 技术实现要点

### AuthService 核心功能
```dart
// 检查登录状态
bool isLoggedIn = await AuthService.instance.isLoggedIn();

// 执行登录
bool success = await AuthService.instance.login(username, password);

// 执行登出
await AuthService.instance.logout();

// 监听状态变化
AuthService.instance.authStateStream.listen((isLoggedIn) {
  // 处理状态变化
});
```

### AuthWrapper 自动路由
```dart
// 根据登录状态自动显示页面
if (isLoggedIn) {
  return MainPage();
} else {
  return SetupPage();
}
```

## 测试验证

运行测试来验证认证系统：
```bash
flutter test test/auth_service_test.dart
```

测试覆盖：
- ✅ 初始状态检查
- ✅ 登录功能测试
- ✅ 登出功能测试
- ✅ 状态流监听测试

## 扩展建议

1. **持久化存储**
   - 集成SharedPreferences
   - 保存用户Token
   - 自动登录功能

2. **真实API集成**
   - 替换模拟登录逻辑
   - 添加网络错误处理
   - 实现Token刷新

3. **安全增强**
   - 使用flutter_secure_storage
   - 添加生物识别认证
   - 实现会话超时

4. **用户体验优化**
   - 添加登录加载动画
   - 实现记住密码功能
   - 优化错误提示信息
