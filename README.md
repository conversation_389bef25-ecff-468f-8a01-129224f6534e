# Align

一个基于Flutter的移动应用，具有完整的用户认证系统和状态管理。

## 功能特性

### 🔐 全局认证状态管理
- **AuthService**: 单例模式的认证服务，管理用户登录状态
- **状态流**: 实时监听认证状态变化
- **自动路由**: 根据登录状态自动跳转到相应页面

### 📱 页面结构
- **SetupPage**: 应用启动引导页
- **LoginPage**: 用户登录页面（支持验证码和密码登录）
- **MainPage**: 主应用界面（底部导航栏）
- **AuthWrapper**: 认证状态包装器，自动管理页面跳转

### 🏗️ 架构设计
```
lib/
├── main.dart                 # 应用入口
├── services/
│   └── auth_service.dart     # 认证服务
├── widgets/
│   └── auth_wrapper.dart     # 认证状态包装器
├── pages/
│   ├── setup_page.dart       # 启动页
│   ├── login_page.dart       # 登录页
│   ├── main_page.dart        # 主页
│   └── home/
│       └── user_profile_page.dart  # 用户资料页（含登出功能）
└── utils/
    ├── color_util.dart       # 颜色工具
    ├── text_util.dart        # 文本样式工具
    └── toast_util.dart       # Toast消息工具
```

## 认证流程

1. **应用启动**: AuthWrapper检查登录状态
2. **未登录**: 显示SetupPage → LoginPage
3. **已登录**: 直接显示MainPage
4. **登录成功**: 更新全局状态，自动跳转到MainPage
5. **登出**: 清除状态，自动跳转到SetupPage

## 使用方法

### 运行应用
```bash
flutter run
```

### 运行测试
```bash
flutter test
```

### 代码分析
```bash
flutter analyze
```

## 技术栈

- **Flutter**: 3.32.0
- **Dart**: 最新稳定版
- **状态管理**: Flutter内置Stream + Singleton模式
- **UI框架**: Cupertino (iOS风格)
- **屏幕适配**: flutter_screenutil
- **网络请求**: dio
- **图片缓存**: cached_network_image
- **消息提示**: ToastUtil (基于fluttertoast)

## 测试覆盖

- ✅ AuthService单元测试
- ✅ 登录状态管理测试
- ✅ 状态流监听测试
- ✅ 登出功能测试

## 开发说明

### 工具类使用规范
**重要**: 开发时必须优先使用项目中的工具类，详见 [工具类文档](./docs/utils/README.md)

```dart
// ✅ 正确：使用工具类
ToastUtil.showSuccess('操作成功');
Container(color: ColorUtil.primaryBgColor);
Text('标题', style: TextUtil.base.sp(18).semiBold);

// ❌ 错误：硬编码或直接使用第三方库
Fluttertoast.showToast(msg: '操作成功');
Container(color: Color(0xFFFFFFFF));
Text('标题', style: TextStyle(fontSize: 18));
```

### 添加新页面
1. 在`lib/pages/`目录下创建新页面
2. 如需认证保护，确保在AuthWrapper管理的路由中
3. 使用项目工具类保持样式一致性

### 扩展认证功能
1. 修改`AuthService`添加新的认证方法
2. 更新登录页面UI和逻辑
3. 添加相应的测试用例

### 状态持久化
当前认证状态仅保存在内存中，可以通过以下方式添加持久化：
- 使用SharedPreferences保存登录状态
- 集成安全存储（如flutter_secure_storage）
- 添加Token管理和刷新机制
