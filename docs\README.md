# Align 项目文档

## 概述

本目录包含了 Align 项目的完整文档，包括架构设计、工具类使用、开发指南等。

## 文档结构

```
docs/
├── README.md                 # 本文档
├── utils/                    # 工具类文档
│   ├── README.md            # 工具类索引
│   ├── toast_util.md        # Toast工具类
│   ├── color_util.md        # 颜色工具类
│   └── text_util.md         # 文本样式工具类
└── ...                      # 其他文档
```

## 快速导航

### 🛠️ 工具类文档

- **[工具类总览](./utils/README.md)** - 所有工具类的索引和使用指南
- **[ToastUtil](./utils/toast_util.md)** - Toast消息统一管理
- **[ColorUtil](./utils/color_util.md)** - 颜色常量管理
- **[TextUtil](./utils/text_util.md)** - 文本样式链式调用

### 📱 功能模块

- **认证系统** - 用户登录、登出、状态管理
- **持久化存储** - SharedPreferences集成
- **UI组件** - 自定义组件和样式

## 开发规范

### 1. 工具类使用

在开发过程中，**必须优先使用项目中的工具类**：

```dart
// ✅ 正确：使用ToastUtil
ToastUtil.showSuccess('操作成功');

// ❌ 错误：直接使用Fluttertoast
Fluttertoast.showToast(msg: '操作成功');
```

```dart
// ✅ 正确：使用ColorUtil
Container(color: ColorUtil.primaryBgColor)

// ❌ 错误：硬编码颜色
Container(color: Color(0xFFFFFFFF))
```

```dart
// ✅ 正确：使用TextUtil
Text('标题', style: TextUtil.base.sp(18).semiBold)

// ❌ 错误：直接创建TextStyle
Text('标题', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600))
```

### 2. 导入顺序

```dart
// 1. Flutter框架
import 'package:flutter/cupertino.dart';

// 2. 第三方包
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 3. 项目工具类（按字母顺序）
import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/utils/toast_util.dart';

// 4. 项目其他文件
import 'package:align/services/auth_service.dart';
import 'package:align/pages/main_page.dart';
```

### 3. 代码组织

```dart
class ExampleWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ColorUtil.primaryBgColor,
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        children: [
          Text(
            '标题',
            style: TextUtil.base.sp(18).semiBold,
          ),
          SizedBox(height: 8.h),
          Text(
            '内容',
            style: TextUtil.base.sp(14).withColor(Colors.grey),
          ),
          SizedBox(height: 16.h),
          CupertinoButton(
            onPressed: () => ToastUtil.showSuccess('操作成功'),
            child: Text('按钮'),
          ),
        ],
      ),
    );
  }
}
```

## 项目特性

### 🔐 认证系统

- **AuthService**: 单例模式的认证服务
- **持久化存储**: 使用SharedPreferences保存登录状态
- **状态流**: 实时监听认证状态变化
- **自动路由**: 根据登录状态自动跳转

### 🎨 UI工具类

- **ToastUtil**: 统一的Toast管理，无logo显示
- **ColorUtil**: 标准颜色常量管理
- **TextUtil**: 链式调用的文本样式构建

### 📱 响应式设计

- **flutter_screenutil**: 屏幕适配解决方案
- **统一单位**: 使用.w、.h、.sp进行尺寸适配

## 开发流程

### 1. 新功能开发

1. **查阅文档**: 确认是否有现有工具类可用
2. **使用工具类**: 优先使用项目中的工具类
3. **扩展工具类**: 如需新功能，扩展现有工具类
4. **更新文档**: 新增功能要更新对应文档

### 2. 代码审查要点

- [ ] 是否使用了项目工具类
- [ ] 是否遵循了导入顺序规范
- [ ] 是否保持了代码风格一致性
- [ ] 是否更新了相关文档

### 3. 测试要求

- [ ] 单元测试覆盖新功能
- [ ] UI测试验证界面正确性
- [ ] 集成测试确保功能完整性

## 常见问题

### Q: 如何添加新的Toast类型？

A: 在 `ToastUtil` 类中添加新的静态方法，参考现有方法的实现。

### Q: 如何定义新的颜色？

A: 在 `ColorUtil` 类中添加新的颜色常量，使用语义化命名。

### Q: 如何扩展文本样式？

A: 在 `TextStyleHelper` extension中添加新的方法，保持链式调用风格。

### Q: 工具类修改会影响现有代码吗？

A: 工具类的修改要保持向后兼容，避免破坏性变更。

## 贡献指南

1. **遵循规范**: 严格按照项目规范开发
2. **使用工具类**: 必须使用项目中的工具类
3. **完善文档**: 新功能要有对应文档
4. **测试覆盖**: 确保代码质量

## 联系方式

如有问题或建议，请联系开发团队。

---

**最后更新**: 2025-01-19  
**版本**: 1.0.0
