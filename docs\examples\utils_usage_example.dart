// 工具类使用示例
// 此文件展示了如何正确使用项目中的工具类

import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 导入项目工具类（按字母顺序）
import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/utils/toast_util.dart';

/// 工具类使用示例页面
class UtilsUsageExamplePage extends StatefulWidget {
  const UtilsUsageExamplePage({super.key});

  @override
  State<UtilsUsageExamplePage> createState() => _UtilsUsageExamplePageState();
}

class _UtilsUsageExamplePageState extends State<UtilsUsageExamplePage> {
  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('工具类使用示例'),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTextUtilExample(),
              SizedBox(height: 24.h),
              _buildColorUtilExample(),
              SizedBox(height: 24.h),
              _buildToastUtilExample(),
              SizedBox(height: 24.h),
              _buildCombinedExample(),
            ],
          ),
        ),
      ),
    );
  }

  /// TextUtil使用示例
  Widget _buildTextUtilExample() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ColorUtil.primaryBgColor,
        border: Border.all(color: ColorUtil.borderGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'TextUtil 使用示例',
            style: TextUtil.base.sp(18).semiBold,
          ),
          SizedBox(height: 12.h),
          Text(
            '基础样式文本',
            style: TextUtil.base,
          ),
          SizedBox(height: 8.h),
          Text(
            '大号粗体文本',
            style: TextUtil.base.sp(20).semiBold,
          ),
          SizedBox(height: 8.h),
          Text(
            '彩色文本示例',
            style: TextUtil.base.sp(16).withColor(CupertinoColors.systemBlue),
          ),
          SizedBox(height: 8.h),
          Text(
            '组合样式：大号+粗体+彩色',
            style: TextUtil.base
                .sp(18)
                .semiBold
                .withColor(CupertinoColors.systemGreen),
          ),
        ],
      ),
    );
  }

  /// ColorUtil使用示例
  Widget _buildColorUtilExample() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ColorUtil.primaryBgColor,
        border: Border.all(color: ColorUtil.borderGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ColorUtil 使用示例',
            style: TextUtil.base.sp(18).semiBold,
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              _buildColorBox('主背景色', ColorUtil.primaryBgColor),
              SizedBox(width: 12.w),
              _buildColorBox('边框灰色', ColorUtil.borderGrey),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '使用ColorUtil确保颜色一致性',
            style: TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
          ),
        ],
      ),
    );
  }

  Widget _buildColorBox(String label, Color color) {
    return Expanded(
      child: Column(
        children: [
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: color,
              border: Border.all(color: CupertinoColors.systemGrey),
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: TextUtil.base.sp(12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// ToastUtil使用示例
  Widget _buildToastUtilExample() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ColorUtil.primaryBgColor,
        border: Border.all(color: ColorUtil.borderGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ToastUtil 使用示例',
            style: TextUtil.base.sp(18).semiBold,
          ),
          SizedBox(height: 12.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              _buildToastButton('成功', () => ToastUtil.showSuccess('操作成功')),
              _buildToastButton('错误', () => ToastUtil.showError('操作失败')),
              _buildToastButton('警告', () => ToastUtil.showWarning('请注意')),
              _buildToastButton('信息', () => ToastUtil.showInfo('提示信息')),
              _buildToastButton('加载', () => ToastUtil.showLoading('正在加载...')),
              _buildToastButton('登录成功', () => ToastUtil.showLoginSuccess()),
              _buildToastButton('网络错误', () => ToastUtil.showNetworkError()),
              _buildToastButton('取消', () => ToastUtil.cancel()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToastButton(String label, VoidCallback onPressed) {
    return CupertinoButton(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      color: CupertinoColors.systemBlue,
      borderRadius: BorderRadius.circular(6.r),
      onPressed: onPressed,
      child: Text(
        label,
        style: TextUtil.base.sp(12).withColor(CupertinoColors.white),
      ),
    );
  }

  /// 组合使用示例
  Widget _buildCombinedExample() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: ColorUtil.primaryBgColor,
        border: Border.all(color: ColorUtil.borderGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '组合使用示例',
            style: TextUtil.base.sp(18).semiBold,
          ),
          SizedBox(height: 12.h),
          Text(
            '这是一个展示如何组合使用多个工具类的示例卡片',
            style: TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
          ),
          SizedBox(height: 16.h),
          CupertinoButton(
            color: CupertinoColors.systemGreen,
            borderRadius: BorderRadius.circular(8.r),
            onPressed: () {
              // 组合使用：点击按钮显示成功Toast
              ToastUtil.showOperationSuccess();
            },
            child: Text(
              '点击显示成功Toast',
              style: TextUtil.base.sp(16).semiBold.withColor(CupertinoColors.white),
            ),
          ),
          SizedBox(height: 8.h),
          CupertinoButton(
            color: CupertinoColors.systemRed,
            borderRadius: BorderRadius.circular(8.r),
            onPressed: () {
              // 组合使用：点击按钮显示错误Toast
              ToastUtil.showOperationFailed();
            },
            child: Text(
              '点击显示失败Toast',
              style: TextUtil.base.sp(16).semiBold.withColor(CupertinoColors.white),
            ),
          ),
        ],
      ),
    );
  }
}

/// 最佳实践示例：自定义组件
class CustomInfoCard extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback? onTap;

  const CustomInfoCard({
    super.key,
    required this.title,
    required this.content,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call();
        // 使用ToastUtil显示反馈
        ToastUtil.showInfo('卡片被点击');
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          // 使用ColorUtil的颜色
          color: ColorUtil.primaryBgColor,
          border: Border.all(color: ColorUtil.borderGrey),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              // 使用TextUtil的链式调用
              style: TextUtil.base.sp(16).semiBold,
            ),
            SizedBox(height: 8.h),
            Text(
              content,
              style: TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
            ),
          ],
        ),
      ),
    );
  }
}
