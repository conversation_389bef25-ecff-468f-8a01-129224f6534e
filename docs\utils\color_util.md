# ColorUtil 工具类文档

## 概述

`ColorUtil` 是项目中的颜色工具类，定义了应用中使用的标准颜色常量，确保UI颜色的一致性。

## 文件位置

```
lib/utils/color_util.dart
```

## 导入方式

```dart
import 'package:align/utils/color_util.dart';
```

## 核心特性

- ✅ **统一颜色管理**：集中定义应用中的所有颜色
- ✅ **语义化命名**：颜色名称清晰表达用途
- ✅ **易于维护**：修改颜色只需在一个地方更新
- ✅ **类型安全**：避免硬编码颜色值

## 可用颜色

### 主要颜色

```dart
// 主背景色
ColorUtil.primaryBgColor

// 边框灰色
ColorUtil.borderGrey
```

## 使用示例

### 在Widget中使用

```dart
Container(
  color: ColorUtil.primaryBgColor,
  decoration: BoxDecoration(
    border: Border.all(color: ColorUtil.borderGrey),
  ),
  child: Text('示例内容'),
)
```

### 在样式中使用

```dart
TextStyle(
  color: ColorUtil.primaryTextColor, // 如果有定义
)
```

## 扩展建议

### 添加新颜色

在 `ColorUtil` 类中添加新的颜色常量：

```dart
class ColorUtil {
  // 现有颜色...
  
  /// 新的颜色定义
  static const Color newColor = Color(0xFF123456);
  
  /// 语义化颜色
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFF9800);
}
```

### 颜色分类建议

```dart
class ColorUtil {
  // 主题色
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFF03DAC6);
  
  // 背景色
  static const Color primaryBgColor = Color(0xFFFFFFFF);
  static const Color secondaryBgColor = Color(0xFFF5F5F5);
  
  // 文本色
  static const Color primaryTextColor = Color(0xFF212121);
  static const Color secondaryTextColor = Color(0xFF757575);
  
  // 边框色
  static const Color borderGrey = Color(0xFFE0E0E0);
  static const Color dividerColor = Color(0xFFBDBDBD);
  
  // 状态色
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color infoColor = Color(0xFF2196F3);
}
```

## 最佳实践

1. **使用语义化命名**：颜色名称应该表达用途而不是具体颜色
2. **避免硬编码**：在代码中直接使用ColorUtil中定义的颜色
3. **保持一致性**：相同用途的元素使用相同的颜色
4. **考虑主题**：为深色模式等主题变化预留扩展空间

## 注意事项

1. **颜色对比度**：确保文本和背景有足够的对比度
2. **无障碍性**：考虑色盲用户的使用体验
3. **品牌一致性**：颜色选择应符合应用的品牌形象
4. **平台适配**：考虑不同平台的颜色显示差异
