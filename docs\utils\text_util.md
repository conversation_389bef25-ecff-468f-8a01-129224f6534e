# TextUtil 工具类文档

## 概述

`TextUtil` 是项目中的文本样式工具类，提供链式调用的TextStyle构建方式，简化文本样式的创建和管理。

## 文件位置

```
lib/utils/text_util.dart
```

## 导入方式

```dart
import 'package:align/utils/text_util.dart';
```

## 核心特性

- ✅ **链式调用**：支持流畅的链式调用语法
- ✅ **响应式设计**：集成flutter_screenutil进行屏幕适配
- ✅ **扩展性强**：通过extension轻松扩展新功能
- ✅ **类型安全**：编译时检查，避免运行时错误

## 基础用法

### 基础样式

```dart
// 获取基础样式（14sp，regular，黑色）
Text('Hello', style: TextUtil.base);
```

### 链式调用示例

```dart
// 设置字体大小
Text('Hello', style: TextUtil.base.sp(16));

// 设置颜色
Text('Hello', style: TextUtil.base.withColor(Colors.blue));

// 设置字体粗细
Text('Hello', style: TextUtil.base.semiBold);

// 组合使用
Text('Hello', style: TextUtil.base.sp(18).semiBold.withColor(Colors.red));
```

## 可用方法和属性

### 基础属性方法

```dart
// 设置字体大小（响应式）
.sp(double size)

// 设置颜色
.withColor(Color color)

// 设置字体
.family(String fontFamily)

// 设置字间距
.letterSpacing(double spacing)

// 设置词间距
.wordSpacing(double spacing)

// 设置行高
.height(double value)
```

### 字体粗细快捷方式

```dart
.semiBold    // FontWeight.w600
// 可以根据需要扩展更多粗细选项
```

## 使用示例

### 标题文本

```dart
Text(
  '页面标题',
  style: TextUtil.base.sp(24).semiBold.withColor(ColorUtil.primaryTextColor),
)
```

### 正文文本

```dart
Text(
  '这是正文内容',
  style: TextUtil.base.sp(16).withColor(ColorUtil.secondaryTextColor),
)
```

### 小标题

```dart
Text(
  '小标题',
  style: TextUtil.base.sp(18).semiBold,
)
```

### 提示文本

```dart
Text(
  '提示信息',
  style: TextUtil.base.sp(12).withColor(Colors.grey),
)
```

## 扩展TextStyleHelper

### 添加新的字体粗细

```dart
extension TextStyleHelper on TextStyle {
  // 现有方法...
  
  /// 粗体
  TextStyle get bold => copyWith(fontWeight: FontWeight.bold);
  
  /// 细体
  TextStyle get light => copyWith(fontWeight: FontWeight.w300);
  
  /// 常规
  TextStyle get regular => copyWith(fontWeight: FontWeight.normal);
  
  /// 中等
  TextStyle get medium => copyWith(fontWeight: FontWeight.w500);
}
```

### 添加颜色快捷方式

```dart
extension TextStyleHelper on TextStyle {
  // 现有方法...
  
  /// 黑色文本
  TextStyle get black => copyWith(color: Colors.black);
  
  /// 白色文本
  TextStyle get white => copyWith(color: Colors.white);
  
  /// 灰色文本
  TextStyle get grey => copyWith(color: Colors.grey);
  
  /// 主色调文本
  TextStyle get primary => copyWith(color: ColorUtil.primaryColor);
}
```

### 添加文本装饰

```dart
extension TextStyleHelper on TextStyle {
  // 现有方法...
  
  /// 下划线
  TextStyle get underline => copyWith(decoration: TextDecoration.underline);
  
  /// 删除线
  TextStyle get lineThrough => copyWith(decoration: TextDecoration.lineThrough);
  
  /// 斜体
  TextStyle get italic => copyWith(fontStyle: FontStyle.italic);
}
```

## 预设样式建议

### 扩展TextUtil基础样式

```dart
class TextUtil {
  // 现有代码...
  
  /// 标题样式
  static TextStyle get title => base.sp(24).semiBold;
  
  /// 副标题样式
  static TextStyle get subtitle => base.sp(18).medium;
  
  /// 正文样式
  static TextStyle get body => base.sp(16);
  
  /// 说明文字样式
  static TextStyle get caption => base.sp(12).withColor(Colors.grey);
  
  /// 按钮文字样式
  static TextStyle get button => base.sp(16).semiBold;
}
```

## 最佳实践

### 调用顺序建议

按照以下顺序进行链式调用，提高代码可读性：

```dart
TextUtil.base
  .sp(16)        // 1. 字体大小
  .semiBold      // 2. 字体粗细
  .withColor()   // 3. 颜色
  .family()      // 4. 字体族
```

### 常用组合

```dart
// 页面标题
TextUtil.base.sp(24).semiBold.withColor(ColorUtil.primaryTextColor)

// 卡片标题
TextUtil.base.sp(18).medium.withColor(ColorUtil.primaryTextColor)

// 正文内容
TextUtil.base.sp(16).withColor(ColorUtil.secondaryTextColor)

// 辅助信息
TextUtil.base.sp(14).withColor(Colors.grey)

// 按钮文字
TextUtil.base.sp(16).semiBold.withColor(Colors.white)
```

## 注意事项

1. **屏幕适配**：使用.sp()方法确保字体大小响应式适配
2. **性能考虑**：避免在build方法中重复创建相同的TextStyle
3. **可读性**：保持链式调用的合理长度，必要时换行
4. **一致性**：在整个应用中使用统一的文本样式规范

## 与其他工具类配合

```dart
// 与ColorUtil配合
Text(
  'Hello',
  style: TextUtil.base.sp(16).withColor(ColorUtil.primaryTextColor),
)

// 在自定义Widget中使用
class CustomButton extends StatelessWidget {
  final String text;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text(
        text,
        style: TextUtil.base.sp(16).semiBold.withColor(Colors.white),
      ),
    );
  }
}
```
