# ToastUtil 工具类文档

## 概述

`ToastUtil` 是项目中统一的Toast工具类，封装了所有Toast相关功能，提供一致的样式和行为，去除了默认的应用图标显示。

## 文件位置

```
lib/utils/toast_util.dart
```

## 导入方式

```dart
import 'package:align/utils/toast_util.dart';
```

## 核心特性

- ✅ **统一样式**：所有Toast使用一致的样式配置
- ✅ **无图标显示**：去除了默认的应用logo显示
- ✅ **多种类型**：支持信息、成功、错误、警告等多种类型
- ✅ **预设方法**：提供常用场景的快捷方法
- ✅ **自定义配置**：支持自定义颜色、位置等参数

## 基础方法

### 通用Toast

```dart
// 普通信息
ToastUtil.showInfo('这是一条信息');

// 成功提示
ToastUtil.showSuccess('操作成功');

// 错误提示
ToastUtil.showError('操作失败');

// 警告提示
ToastUtil.showWarning('请注意');

// 加载提示（长时间显示）
ToastUtil.showLoading('正在加载...');
```

### 位置控制

```dart
// 顶部显示
ToastUtil.showTop('顶部消息');

// 底部显示
ToastUtil.showBottom('底部消息');

// 中心显示（默认）
ToastUtil.showInfo('中心消息');
```

### 自定义Toast

```dart
ToastUtil.showCustom(
  message: '自定义消息',
  backgroundColor: CupertinoColors.systemPurple,
  textColor: CupertinoColors.white,
  fontSize: 18.0,
  gravity: ToastGravity.TOP,
  toastLength: Toast.LENGTH_LONG,
  timeInSecForIosWeb: 3,
);
```

## 预设场景方法

### 登录相关

```dart
ToastUtil.showLoginSuccess();           // 登录成功
ToastUtil.showLoginFailed();            // 登录失败（默认消息）
ToastUtil.showLoginFailed('用户名错误'); // 登录失败（自定义消息）
ToastUtil.showLogoutSuccess();          // 登出成功
```

### 网络相关

```dart
ToastUtil.showNetworkError();           // 网络错误（默认消息）
ToastUtil.showNetworkError('连接超时'); // 网络错误（自定义消息）
ToastUtil.showServerError();            // 服务器错误
```

### 数据操作

```dart
ToastUtil.showDataLoading();            // 数据加载中
ToastUtil.showDataLoadFailed();         // 数据加载失败
ToastUtil.showSaveSuccess();            // 保存成功
ToastUtil.showSaveFailed();             // 保存失败
ToastUtil.showDeleteSuccess();          // 删除成功
ToastUtil.showDeleteFailed();           // 删除失败
```

### 文件操作

```dart
ToastUtil.showDownloadStart();          // 开始下载
ToastUtil.showDownloadSuccess();        // 下载完成
ToastUtil.showDownloadFailed();         // 下载失败
ToastUtil.showUploadStart();            // 开始上传
ToastUtil.showUploadSuccess();          // 上传成功
ToastUtil.showUploadFailed();           // 上传失败
```

### 其他操作

```dart
ToastUtil.showCopySuccess();            // 复制成功
ToastUtil.showShareSuccess();           // 分享成功
ToastUtil.showPermissionError();        // 权限错误
ToastUtil.showValidationError('输入无效'); // 输入验证错误
ToastUtil.showOperationSuccess();       // 操作成功
ToastUtil.showOperationFailed();        // 操作失败
```

### 同步相关

```dart
ToastUtil.showSyncStart();              // 开始同步
ToastUtil.showSyncSuccess();            // 同步完成
ToastUtil.showSyncFailed();             // 同步失败
```

## 控制方法

```dart
// 取消当前显示的Toast
ToastUtil.cancel();
```

## 样式配置

### 默认配置

- **持续时间**: 2秒
- **字体大小**: 16.0
- **显示位置**: 居中
- **背景色**: 半透明黑色
- **文字色**: 白色

### 不同类型的颜色

- **普通信息**: 黑色背景 (alpha: 0.8)
- **成功**: 绿色背景 (alpha: 0.9)
- **错误**: 红色背景 (alpha: 0.9)
- **警告**: 橙色背景 (alpha: 0.9)
- **加载**: 蓝色背景 (alpha: 0.9)

## 使用示例

### 登录页面

```dart
// 验证失败
ToastUtil.showValidationError('请先同意用户协议和隐私政策');

// 登录失败
ToastUtil.showLoginFailed();

// 异常处理
ToastUtil.showError('登录过程中发生错误: $e');
```

### 网络请求

```dart
try {
  ToastUtil.showDataLoading();
  final result = await apiCall();
  ToastUtil.cancel(); // 取消加载提示
  ToastUtil.showOperationSuccess();
} catch (e) {
  ToastUtil.cancel();
  if (e is NetworkException) {
    ToastUtil.showNetworkError();
  } else {
    ToastUtil.showServerError();
  }
}
```

## 注意事项

1. **避免频繁调用**: Toast显示期间避免重复调用，可使用 `cancel()` 方法先取消当前Toast
2. **消息长度**: 保持消息简洁明了，避免过长的文本
3. **用户体验**: 重要操作建议使用对话框而非Toast
4. **测试验证**: 在不同设备和系统版本上测试Toast显示效果

## 扩展方法

如需添加新的Toast类型，在 `ToastUtil` 类中添加静态方法：

```dart
/// 显示自定义场景Toast
static void showCustomScenario() {
  showCustom(
    message: '自定义场景消息',
    backgroundColor: CupertinoColors.systemTeal,
    // 其他配置...
  );
}
```
