import 'package:align/widgets/auth_wrapper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(393, 852), // iPhone 15 Pro design size
      builder: (context, child) {
        return const CupertinoApp(
          title: 'Align',
          theme: CupertinoThemeData(
            primaryColor: CupertinoColors.systemBlue,
          ),
          home: AuthWrapper(),
        );
      },
    );
  }
}
