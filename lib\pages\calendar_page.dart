import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/widgets/app_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CalendarPage extends StatefulWidget {
  const CalendarPage({super.key});

  @override
  State<CalendarPage> createState() => _CalendarPageState();
}

class _CalendarPageState extends State<CalendarPage> {
  final Color _card1BgColor = const Color(0xFFF5F5F5);
  final Color _card2Color = const Color(0xFFFE2C55);
  final Color _card2BgColor = const Color(0xFFFFE3E3);
  final Color _card3Color = const Color(0xFF2C8EFE);
  final Color _card3BgColor = const Color(0xFFE3F1FF);
  final Color _card4Color = const Color(0xFFFEC62C);
  final Color _card4BgColor = const Color(0xFFFFF9E3);

  final Color _progressColor1 = const Color(0xFFFFC4C4);
  final Color _progressColor2 = const Color(0xFF2C8EFE);
  final Color _progressColor3 = const Color(0xFFFEC62C);

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              SizedBox(height: 24.h),
              _buildWeekView(),
              SizedBox(height: 24.h),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildPregnancyCard(),
                      SizedBox(height: 16.h),
                      _buildTodoCard(),
                      SizedBox(height: 16.h),
                      _buildSymptomCard(),
                      SizedBox(height: 16.h),
                      _buildDiaryCard(),
                      SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('今天', style: TextUtil.base.sp(24).semiBold),
        Row(
          children: [
            AppButton(
              onPressed: () {},
              child: Icon(
                CupertinoIcons.search,
                size: 24.sp,
                color: CupertinoColors.black,
              ),
            ),
            SizedBox(width: 16.w),
            AppButton(
              onPressed: () {},
              child: Icon(
                CupertinoIcons.add,
                size: 24.sp,
                color: CupertinoColors.black,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWeekView() {
    final days = ['25', '26', '27', '28', '29', '30', '31'];
    final daysView = List.generate(days.length, (index) {
      bool isSelected = days[index] == '28';
      return Container(
        width: 44.r,
        height: 44.r,
        margin: EdgeInsets.symmetric(horizontal: 4.w),
        decoration: BoxDecoration(
          color: isSelected
              ? ColorUtil.primaryBgColor
              : CupertinoColors.transparent,
          borderRadius: BorderRadius.circular(22.r),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              days[index],
              style: TextUtil.base
                  .sp(16)
                  .semiBold
                  .withColor(
                    isSelected ? CupertinoColors.white : CupertinoColors.black,
                  ),
            ),
          ],
        ),
      );
    });
    return Row(
      children: [
        AppButton(
          onPressed: () {},
          child: Icon(
            CupertinoIcons.left_chevron,
            size: 20.sp,
            color: CupertinoColors.inactiveGray,
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: daysView,
            ),
          ),
        ),
        AppButton(
          onPressed: () {},
          child: Icon(
            CupertinoIcons.right_chevron,
            size: 20.sp,
            color: CupertinoColors.inactiveGray,
          ),
        ),
      ],
    );
  }

  Widget _buildPregnancyCard() {
    return Container(
      decoration: BoxDecoration(
        color: _, // 浅粉色背景
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // 顶部进度条
          _buildProgressBar(),

          Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              children: [
                // 标题和倒计时区域
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 左侧：孕期信息
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                          decoration: BoxDecoration(
                            color: CupertinoColors.white,
                            borderRadius: BorderRadius.circular(20.r),
                          ),
                          child: Text(
                            '孕中期',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                              color: CupertinoColors.black,
                            ),
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          '第15周 第3天',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                      ],
                    ),

                    // 右侧：倒计时
                    Column(
                      children: [
                        Text(
                          '倒计时',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Row(
                          children: [
                            _buildCountdownNumber('2'),
                            SizedBox(width: 2.w),
                            _buildCountdownNumber('5'),
                            SizedBox(width: 2.w),
                            _buildCountdownNumber('4'),
                            SizedBox(width: 8.w),
                            Text(
                              '天',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: CupertinoColors.black,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          '周',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                SizedBox(height: 24.h),

                // 中间：宝宝大小展示
                _buildBabySizeDisplay(),

                SizedBox(height: 24.h),

                // 这周概括
                _buildWeeklySummary(),

                SizedBox(height: 20.h),

                // 底部按钮
                Row(
                  children: [
                    Expanded(
                      child: _buildActionButton('关于宝宝'),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildActionButton('关于自己'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    // 孕期进度：30周(孕早期) + 37.5周(孕中期) + 32.5周(孕晚期) = 100周总长度
    // 实际孕期40周，所以比例为 30/100, 37.5/100, 32.5/100
    // 当前在第15周，属于孕中期，在孕早期30周后的第15周位置
    // 所以当前位置 = (30 + 15) / 100 = 45%

    return Stack(
      children: [
        Container(
          height: 8.h,
          margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Stack(
            children: [
              // 背景进度条，显示三个阶段的颜色
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  gradient: LinearGradient(
                    colors: [
                      _progressColor1, // 孕早期 - 粉色
                      _progressColor1, // 孕早期结束
                      _progressColor2, // 孕中期 - 蓝色
                      _progressColor2, // 孕中期结束
                      _progressColor3, // 孕晚期 - 黄色
                      _progressColor3, // 孕晚期 - 黄色
                    ],
                    stops: const [
                      0.0,    // 孕早期开始
                      0.3,    // 孕早期结束 (30%)
                      0.3,    // 孕中期开始
                      0.675,  // 孕中期结束 (30% + 37.5% = 67.5%)
                      0.675,  // 孕中期结束 (30% + 37.5% = 67.5%)
                      1.0,    // 孕晚期结束
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
              // 当前进度位置的爱心标记
              
            ],
          ),
        ),
        Positioned(
            left: MediaQuery.of(context).size.width * 0.45 - 40.w, // 45%位置，减去爱心宽度的一半
            child: Center(
              child: Icon(
                CupertinoIcons.suit_heart_fill,
                size: 20.r,
                color: CupertinoColors.systemRed,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCountdownNumber(String number) {
    return Container(
      width: 24.w,
      height: 28.h,
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: const Color(0xFFE0E0E0),
          width: 1.w,
        ),
      ),
      child: Center(
        child: Text(
          number,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: CupertinoColors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildBabySizeDisplay() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          // 左侧：草莓图片
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              color: const Color(0xFFFFF0F0),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Center(
              child: Text(
                '🍓', // 草莓emoji，也可以用图片替换
                style: TextStyle(fontSize: 32.sp),
              ),
            ),
          ),

          SizedBox(width: 16.w),

          // 右侧：文字描述
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '宝宝这周有\n这么大了',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: CupertinoColors.black,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklySummary() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '这周概括',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: CupertinoColors.black,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '怀孕15周时，胎儿的四肢活动，毛发开始生长，孕妇能感受到胎儿的微弱活动，孕妇应该可能感觉到胎动。',
            style: TextStyle(
              fontSize: 14.sp,
              color: CupertinoColors.systemGrey,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String text) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: const Color(0xFFE0E0E0),
          width: 1.w,
        ),
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: CupertinoColors.black,
          ),
        ),
      ),
    );
  }







  Widget _buildTodoCard() {
    return _buildCard(
      color: _card2BgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.r,
                height: 38.r,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(999.r),
                ),
                child: ImageIcon(
                  AssetImage('images/pages/calendar/card2.png'),
                  size: 20.sp,
                  color: _card2Color,
                ),
              ),
              SizedBox(width: 8.w),
              Text('要做的事', style: TextUtil.base.sp(18).semiBold),
            ],
          ),
          SizedBox(height: 16.h),
          _buildTodoItem('看医生', '北京第三医院', '09:00', '10:30'),
          SizedBox(height: 12.h),
          _buildTodoItem('去领礼物', '别忘了去这里。。。', '18:00', '18:30'),
          SizedBox(height: 12.h),
          _buildTodoItem('吃药', '2颗药 + 3mg', '21:00', ''),
        ],
      ),
    );
  }

  Widget _buildTodoItem(
    String title,
    String subtitle,
    String startTime,
    String endTime,
  ) {
    return Row(
      children: [
        Container(width: 4.w, height: 40.h, color: const Color(0xFFFF7D7D)),
        SizedBox(width: 12.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextUtil.base.sp(16).medium),
            SizedBox(height: 2.h),
            Text(subtitle, style: TextUtil.base.sp(12).grey),
          ],
        ),
        const Spacer(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(startTime, style: TextUtil.base.sp(12).black),
            SizedBox(height: 2.h),
            if (endTime.isNotEmpty)
              Text(endTime, style: TextUtil.base.sp(12).grey),
          ],
        ),
      ],
    );
  }

  Widget _buildSymptomCard() {
    return _buildCard(
      color: _card3BgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.r,
                height: 38.r,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(999.r),
                ),
                child: ImageIcon(
                  AssetImage('images/pages/calendar/card3.png'),
                  size: 20.sp,
                  color: _card3Color,
                ),
              ),
              SizedBox(width: 8.w),
              Text('症状', style: TextUtil.base.sp(18).semiBold),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  children: [
                    _buildSymptomItem('心情', '开心\n冷静', CupertinoIcons.smiley),
                    SizedBox(height: 12.h),
                    _buildSymptomItem('睡眠质量', '8小时', CupertinoIcons.moon),
                    SizedBox(height: 12.h),
                    _buildSymptomItem(
                      '饮食',
                      '300g 水果\n500g 蔬菜\n100g 粗品',
                      CupertinoIcons.tuningfork,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  children: [
                    _buildSymptomItem(
                      '运动',
                      '1000m 游泳\n45分钟 散步',
                      CupertinoIcons.sportscourt,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSymptomItem(String title, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(width: 4.w, height: 24.h, color: _card3Color),
            SizedBox(width: 8.w),
            Icon(icon, size: 20.sp, color: _card3Color),
          ],
        ),
        SizedBox(width: 8.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextUtil.base.sp(16).medium),
            SizedBox(height: 4.h),
            Text(value, style: TextUtil.base.sp(12).grey),
          ],
        ),
      ],
    );
  }

  Widget _buildDiaryCard() {
    return _buildCard(
      color: _card4BgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.r,
                height: 38.r,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(999.r),
                ),
                child: ImageIcon(
                  AssetImage('images/pages/calendar/card4.png'),
                  size: 20.sp,
                  color: _card4Color,
                ),
              ),
              SizedBox(width: 8.w),
              Text('日记', style: TextUtil.base.sp(18).semiBold),
            ],
          ),
          SizedBox(height: 16.h),
          _buildDiaryItem('小小想法😌', '一些今天的感受和想法记录下来...', '8:28 AM'),
          SizedBox(height: 12.h),
          _buildDiaryItem('今日总结😌', '每天可以记录多个笔记', '5:13 PM'),
        ],
      ),
    );
  }

  Widget _buildDiaryItem(String title, String subtitle, String time) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: Image.asset(
            'images/placeholder.png', // Placeholder for cat image
            width: 52.r,
            height: 52.r,
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: TextUtil.base.sp(16).medium),
              SizedBox(height: 2.h),
              Text(subtitle, style: TextUtil.base.sp(12).grey),
            ],
          ),
        ),
        SizedBox(width: 12.w),
        Text(time, style: TextUtil.base.sp(12).grey),
      ],
    );
  }

  Widget _buildCard({required Widget child, Color? color}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color ?? CupertinoColors.white,
        borderRadius: BorderRadius.circular(20.r),
        // boxShadow: [
        //   BoxShadow(
        //     color: CupertinoColors.systemGrey5.withOpacity(0.5),
        //     spreadRadius: 1,
        //     blurRadius: 10,
        //     offset: const Offset(0, 5),
        //   ),
        // ],
      ),
      child: child,
    );
  }
}
