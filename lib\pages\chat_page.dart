import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/widgets/app_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: CupertinoPageScaffold(
        backgroundColor: CupertinoColors.white,
        child: <PERSON><PERSON><PERSON>(
          child: Container(
            padding: EdgeInsets.all(24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    AppButton(
                      onPressed: () {
                        // TODO: 侧边栏功能
                      },
                      child: Icon(
                        CupertinoIcons.sidebar_left,
                        size: 22.sp,
                        color: CupertinoColors.black,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    AppButton(
                      onPressed: () {
                        // TODO: 新对话功能
                      },
                      child: Container(
                        width: 76.w,
                        height: 36.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18.r),
                          border: Border.all(
                            color: CupertinoColors.black,
                            width: 1,
                            style: BorderStyle.solid,
                          ),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '+ 新对话',
                          style: TextUtil.base.sp(14).medium,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 60.h),
                Text(
                  '下午好，有什么我能帮你的吗？',
                  style: TextUtil.base.sp(24).semiBold,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 60.h),
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: ColorUtil.borderGrey, width: 1),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ConstrainedBox(
                            constraints: BoxConstraints(maxHeight: 120.h),
                            child: CupertinoTextField(
                              focusNode: _focusNode,
                              placeholder: '发消息，输入您想说的话',
                              placeholderStyle: TextUtil.base.grey,
                              style: TextUtil.base,
                              decoration: const BoxDecoration(),
                              maxLines: null,
                              keyboardType: TextInputType.multiline,
                            ),
                          ),
                          SizedBox(height: 12.h),
                          Row(
                            children: [
                              AppButton(
                                onPressed: () {
                                  // TODO: 附件功能
                                },
                                child: Icon(
                                  CupertinoIcons.paperclip,
                                  color: CupertinoColors.inactiveGray,
                                  size: 22.sp,
                                ),
                              ),
                              const Spacer(),
                              AppButton(
                                onPressed: () {
                                  // TODO: 语音功能
                                },
                                child: Icon(
                                  CupertinoIcons.mic,
                                  color: CupertinoColors.inactiveGray,
                                  size: 22.sp,
                                ),
                              ),
                              SizedBox(width: 10.w),
                              Container(
                                width: 1,
                                height: 22.h,
                                color: ColorUtil.borderGrey,
                              ),
                              SizedBox(width: 10.w),
                              AppButton(
                                onPressed: () {
                                  // TODO: 发送功能
                                },
                                child: Container(
                                  width: 32.w,
                                  height: 32.h,
                                  decoration: BoxDecoration(
                                    color: CupertinoColors.black,
                                    borderRadius: BorderRadius.circular(16.r),
                                  ),
                                  child: Icon(
                                    CupertinoIcons.arrow_up,
                                    color: CupertinoColors.white,
                                    size: 16.sp,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Row(
                      children: [
                        Expanded(child: _buildQuickButton('总结', CupertinoIcons.star)),
                        SizedBox(width: 12.w),
                        Expanded(child: _buildQuickButton('进展', CupertinoIcons.heart)),
                        SizedBox(width: 12.w),
                        Expanded(child: _buildQuickButton('更多', CupertinoIcons.search)),
                      ],
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickButton(String text, IconData icon) {
    return AppButton(
      onPressed: () {
        // TODO: 快捷按钮功能
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        decoration: BoxDecoration(
          border: Border.all(color: ColorUtil.borderGrey, width: 1),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 16.sp, color: CupertinoColors.black),
            SizedBox(width: 6.w),
            Text(
              text,
              style: TextUtil.base.medium,
            ),
          ],
        ),
      ),
    );
  }
}
