import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/widgets/poll_card.dart';
import 'package:align/widgets/post_card.dart';
import 'package:align/widgets/question_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:waterfall_flow/waterfall_flow.dart';

class RecommendedPage extends StatelessWidget {
  const RecommendedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CupertinoColors.systemBackground,
      body: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: EdgeInsets.fromLTRB(24.w, 12.h, 24.w, 0),
            sliver: SliverToBoxAdapter(
              child: _buildFilters(),
            ),
          ),
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
            sliver: _buildRecommendationGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Row(
      children: [
        _buildFilterChip('筛选'),
        SizedBox(width: 8.w),
        _buildFilterChip('排序'),
        SizedBox(width: 8.w),
        Expanded(
          child: Container(
            height: 32.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: ColorUtil.grey,
              borderRadius: BorderRadius.circular(999.r),
            ),
            child: Row(
              children: [
                Text('搜索', style: TextUtil.base.sp(14).medium),
                const Spacer(),
                Icon(CupertinoIcons.search, size: 18.sp, color: CupertinoColors.black),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label) {
    return Container(
      width: 68.w,
      height: 32.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: ColorUtil.borderGrey, width: 1.w),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(label, style: TextUtil.base.sp(14).medium),
          SizedBox(width: 4.w),
          Icon(CupertinoIcons.chevron_down, size: 14.sp, color: ColorUtil.borderGrey),
        ],
      ),
    );
  }

  Widget _buildRecommendationGrid() {
    return SliverWaterfallFlow(
      gridDelegate: SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == 1) {
            return const QuestionCard(
              question: '有人也正在经历非常严重的孕吐吗？你们是怎么缓解的？',
              userImage: 'images/icon/apple.png',
              userName: '用户 112',
            );
          }
          if (index == 4) {
            return const PollCard(
              question: '那个更好？',
              options: ['选择 1', '选择 2'],
              userImage: 'images/icon/apple.png',
              userName: '用户 116',
            );
          }
          if (index == 5) {
            return const QuestionCard(
              question: '突然特别想吃榴莲，你们有什么孕期cravings？',
              userImage: 'images/icon/apple.png',
              userName: '用户 112',
            );
          }
          return PostCard(
            image: 'images/placeholder.png',
            title: _getPostTitle(index),
            userImage: 'images/icon/apple.png',
            userName: _getUserName(index),
            imageHeight: _getImageHeight(index),
          );
        },
        childCount: 6,
      ),
    );
  }

  String _getPostTitle(int index) {
    switch (index) {
      case 0:
        return '这周末常识了一下';
      case 2:
        return '分享一下我最近的一些收获';
      case 3:
        return '第一次有这种想法';
      default:
        return '这是一个标题';
    }
  }

  String _getUserName(int index) {
        return '用户';
  }

  double _getImageHeight(int index) {
    switch (index) {
      case 0:
        return 180.0;
      case 2:
        return 220.0;
      case 3:
        return 160.0;
      default:
        return 200.0;
    }
  }
} 