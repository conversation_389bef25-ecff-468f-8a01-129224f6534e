import 'package:align/services/auth_service.dart';
import 'package:align/pages/storage_demo_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserProfilePage extends StatelessWidget {
  const UserProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            '用户个人资料',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 40.h),

          // 用户信息区域
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: CupertinoColors.systemGrey6,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                const Icon(
                  CupertinoIcons.person_circle,
                  size: 80,
                  color: CupertinoColors.systemGrey,
                ),
                SizedBox(height: 16.h),
                const Text(
                  '演示用户',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8.h),
                const Text(
                  '<EMAIL>',
                  style: TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 40.h),

          // 存储演示按钮
          CupertinoButton(
            color: CupertinoColors.systemBlue,
            borderRadius: BorderRadius.circular(12.r),
            onPressed: () => _navigateToStorageDemo(context),
            child: const Text(
              '查看存储演示',
              style: TextStyle(
                color: CupertinoColors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // 登出按钮
          CupertinoButton(
            color: CupertinoColors.systemRed,
            borderRadius: BorderRadius.circular(12.r),
            onPressed: () => _handleLogout(context),
            child: const Text(
              '登出',
              style: TextStyle(
                color: CupertinoColors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToStorageDemo(BuildContext context) {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => const StorageDemoPage(),
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    // 显示确认对话框
    final bool? shouldLogout = await showCupertinoDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('确认登出'),
          content: const Text('您确定要登出吗？'),
          actions: [
            CupertinoDialogAction(
              child: const Text('取消'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              child: const Text('登出'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      // 执行登出
      await AuthService.instance.logout();
      // AuthWrapper会自动监听状态变化并跳转到登录页面
    }
  }
}