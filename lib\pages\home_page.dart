import 'package:align/pages/home/<USER>';
import 'package:align/pages/home/<USER>';
import 'package:align/pages/home/<USER>';
import 'package:align/pages/home/<USER>';
import 'package:align/utils/text_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedTab = 1;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _selectedTab);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.systemBackground,
        border: null,
        middle: _buildTabs(),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: PageView(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _selectedTab = index;
            });
          },
          children: const [
            FollowingPage(),
            RecommendedPage(),
            SavedPage(),
            UserProfilePage(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabs() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildTabItem('关注', 0),
        _buildTabItem('推荐', 1),
        _buildTabItem('收藏', 2),
        _buildTabItem('个人主页', 3),
      ],
    );
  }

  Widget _buildTabItem(String title, int index) {
    final bool isSelected = _selectedTab == index;
    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title,
            style: isSelected ? TextUtil.base.sp(16).semiBold.black : TextUtil.base.sp(16).medium.grey,
          ),
          SizedBox(height: 8.h),
          Container(
            height: 2.h,
            width: 20.w,
            color: isSelected
                ? CupertinoColors.black
                : CupertinoColors.transparent,
          ),
        ],
      ),
    );
  }
} 