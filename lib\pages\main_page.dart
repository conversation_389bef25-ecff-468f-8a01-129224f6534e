import 'package:align/pages/chat_page.dart';
import 'package:align/pages/calendar_page.dart';
import 'package:align/pages/discover_page.dart';
import 'package:align/pages/home_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {

  late final CupertinoTabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = CupertinoTabController(initialIndex: 1);
  }

  Widget _buildTabIcon(String imagePath) {
    return Padding(
      padding: EdgeInsets.only(top: 4.h),
      child: ImageIcon(
        AssetImage(imagePath),
        size: 24.sp,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoTabScaffold(
      controller: _tabController,
      tabBar: CupertinoTabBar(
        backgroundColor: CupertinoColors.white,
        activeColor: const Color(0xFF000000),
        inactiveColor: const Color(0xFFDADADA),
        border: const Border(
          top: BorderSide(
            color: CupertinoColors.separator,
            width: 0.5,
          ),
        ),
        height: 60.h,
        items: [
          BottomNavigationBarItem(
            icon: _buildTabIcon('images/icon/tabbar/tab1.png'),
          ),
          BottomNavigationBarItem(
            icon: _buildTabIcon('images/icon/tabbar/tab2.png'),
          ),
          BottomNavigationBarItem(
            icon: _buildTabIcon('images/icon/tabbar/tab3.png'),
          ),
          BottomNavigationBarItem(
            icon: _buildTabIcon('images/icon/tabbar/tab4.png'),
          ),
        ],
      ),
      tabBuilder: (context, index) {
        switch (index) {
          case 0:
            return const ChatPage();
          case 1:
            return const CalendarPage();
          case 2:
            return const DiscoverPage();
          case 3:
            return const HomePage();
          default:
            return const CalendarPage();
        }
      },
    );
  }
} 