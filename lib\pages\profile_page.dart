import 'package:align/utils/color_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: ColorUtil.primaryBgColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.white,
        border: const Border(
          bottom: BorderSide(
            color: CupertinoColors.separator,
            width: 0.5,
          ),
        ),
        middle: Text(
          '我的',
          style: TextStyle(
            fontSize: 17.sp,
            fontWeight: FontWeight.w600,
            color: CupertinoColors.black,
          ),
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          child: Icon(
            CupertinoIcons.settings,
            size: 24.sp,
            color: CupertinoColors.systemBlue,
          ),
          onPressed: () {
            // TODO: 打开设置
          },
        ),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 头像
              Container(
                width: 80.w,
                height: 80.h,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(40.r),
                  border: Border.all(
                    color: ColorUtil.primaryBgColor,
                    width: 3.w,
                  ),
                ),
                child: Icon(
                  CupertinoIcons.person_fill,
                  size: 40.sp,
                  color: ColorUtil.primaryBgColor,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                '用户名',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.black,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                '<EMAIL>',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: CupertinoColors.inactiveGray,
                ),
              ),
              SizedBox(height: 32.h),
              Text(
                '个人中心功能即将上线',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: CupertinoColors.black,
                ),
              ),
              SizedBox(height: 12.h),
              Text(
                '更多个性化设置等你来体验',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: CupertinoColors.inactiveGray,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 