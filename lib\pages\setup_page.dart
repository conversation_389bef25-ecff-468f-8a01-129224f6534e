import 'package:align/pages/login_page.dart';
import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SetupPage extends StatelessWidget {
  const SetupPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const LoginPage(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
          ),
        );
      },
      child: CupertinoPageScaffold(
        backgroundColor: ColorUtil.primaryBgColor,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Align',
                style: TextUtil.base.sp(34).semiBold,
              ),
              SizedBox(height: 16.h),
              Text(
                '点击任意位置开始使用',
                style: TextUtil.base.black,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 