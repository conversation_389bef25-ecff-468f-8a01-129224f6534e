import 'package:align/services/auth_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 持久化存储演示页面
class StorageDemoPage extends StatefulWidget {
  const StorageDemoPage({super.key});

  @override
  State<StorageDemoPage> createState() => _StorageDemoPageState();
}

class _StorageDemoPageState extends State<StorageDemoPage> {
  bool? _storedLoginStatus;
  bool _currentLoginStatus = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
    _listenToAuthChanges();
  }

  /// 加载存储信息
  Future<void> _loadStorageInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedStatus = prefs.getBool('is_logged_in');
      final currentStatus = await AuthService.instance.isLoggedIn();
      
      setState(() {
        _storedLoginStatus = storedStatus;
        _currentLoginStatus = currentStatus;
        _statusMessage = '存储状态加载完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '加载失败: $e';
      });
    }
  }

  /// 监听认证状态变化
  void _listenToAuthChanges() {
    AuthService.instance.authStateStream.listen((isLoggedIn) {
      setState(() {
        _currentLoginStatus = isLoggedIn;
      });
      _loadStorageInfo(); // 重新加载存储信息
    });
  }

  /// 模拟登录
  Future<void> _simulateLogin() async {
    setState(() {
      _statusMessage = '正在登录...';
    });

    final success = await AuthService.instance.login(
      username: 'demo_user',
      password: 'demo_password',
    );

    setState(() {
      _statusMessage = success ? '登录成功！' : '登录失败！';
    });
  }

  /// 模拟登出
  Future<void> _simulateLogout() async {
    setState(() {
      _statusMessage = '正在登出...';
    });

    await AuthService.instance.logout();

    setState(() {
      _statusMessage = '登出成功！';
    });
  }

  /// 清除所有存储数据
  Future<void> _clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      setState(() {
        _storedLoginStatus = null;
        _statusMessage = '所有存储数据已清除';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '清除失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('持久化存储演示'),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 状态信息卡片
              _buildStatusCard(),
              SizedBox(height: 20.h),
              
              // 操作按钮
              _buildActionButtons(),
              SizedBox(height: 20.h),
              
              // 状态消息
              _buildStatusMessage(),
              SizedBox(height: 20.h),
              
              // 说明文本
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '登录状态信息',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          _buildStatusRow('SharedPreferences存储', _getStorageStatusText()),
          SizedBox(height: 8.h),
          _buildStatusRow('当前内存状态', _currentLoginStatus ? '已登录' : '未登录'),
          SizedBox(height: 8.h),
          _buildStatusRow('状态一致性', _isStatusConsistent() ? '✅ 一致' : '❌ 不一致'),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14.sp),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: _getStatusColor(value),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: CupertinoButton(
                color: CupertinoColors.systemBlue,
                onPressed: _simulateLogin,
                child: const Text('模拟登录'),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: CupertinoButton(
                color: CupertinoColors.systemRed,
                onPressed: _simulateLogout,
                child: const Text('模拟登出'),
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: CupertinoButton(
                color: CupertinoColors.systemGrey,
                onPressed: _loadStorageInfo,
                child: const Text('刷新状态'),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: CupertinoButton(
                color: CupertinoColors.systemOrange,
                onPressed: _clearAllData,
                child: const Text('清除数据'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusMessage() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: CupertinoColors.systemYellow.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: CupertinoColors.systemYellow),
      ),
      child: Text(
        _statusMessage.isEmpty ? '等待操作...' : _statusMessage,
        style: TextStyle(fontSize: 14.sp),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '使用说明：',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '1. 点击"模拟登录"会将登录状态保存到SharedPreferences\n'
            '2. 点击"模拟登出"会清除SharedPreferences中的登录状态\n'
            '3. 重启应用后，登录状态会从SharedPreferences中恢复\n'
            '4. "刷新状态"可以重新加载存储信息\n'
            '5. "清除数据"会清除所有SharedPreferences数据',
            style: TextStyle(fontSize: 14.sp),
          ),
        ],
      ),
    );
  }

  String _getStorageStatusText() {
    if (_storedLoginStatus == null) {
      return '无数据';
    }
    return _storedLoginStatus! ? '已登录' : '未登录';
  }

  bool _isStatusConsistent() {
    if (_storedLoginStatus == null) {
      return !_currentLoginStatus;
    }
    return _storedLoginStatus == _currentLoginStatus;
  }

  Color _getStatusColor(String status) {
    if (status.contains('已登录') || status.contains('✅')) {
      return CupertinoColors.systemGreen;
    } else if (status.contains('未登录') || status.contains('❌')) {
      return CupertinoColors.systemRed;
    }
    return CupertinoColors.label;
  }
}
