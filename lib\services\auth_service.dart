import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

/// 认证服务，管理用户登录状态
class AuthService {
  static final AuthService _instance = AuthService._internal();
  static AuthService get instance => _instance;

  AuthService._internal();

  // 登录状态流控制器
  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();

  // 登录状态流
  Stream<bool> get authStateStream => _authStateController.stream;

  // 当前登录状态
  bool _isLoggedIn = false;

  // SharedPreferences 键名
  static const String _keyIsLoggedIn = 'is_logged_in';

  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;
      return _isLoggedIn;
    } catch (e) {
      // 如果读取失败，默认为未登录状态
      _isLoggedIn = false;
      return false;
    }
  }
  
  /// 登录
  Future<bool> login({required String username, required String password}) async {
    try {

      // 这里添加实际的登录逻辑，比如调用API
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 模拟登录成功
      _isLoggedIn = true;

      // 保存登录状态到SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyIsLoggedIn, true);

      // 通知状态变化
      _authStateController.add(_isLoggedIn);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 清除SharedPreferences中的登录状态
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyIsLoggedIn);

      // 更新内存状态
      _isLoggedIn = false;

      // 通知状态变化
      _authStateController.add(_isLoggedIn);
    } catch (e) {
      // 即使清除失败，也要更新内存状态
      _isLoggedIn = false;
      _authStateController.add(_isLoggedIn);
    }
  }
  
  /// 获取当前登录状态（同步）
  bool get currentLoginStatus => _isLoggedIn;
  
  /// 释放资源
  void dispose() {
    _authStateController.close();
  }
}
