import 'package:flutter/cupertino.dart';
import 'package:fluttertoast/fluttertoast.dart';

/// Toast工具类，统一管理应用中的Toast显示
class ToastUtil {
  ToastUtil._();

  /// 默认Toast配置
  static const Duration _defaultDuration = Duration(seconds: 2);
  static const double _defaultFontSize = 16.0;
  static const ToastGravity _defaultGravity = ToastGravity.CENTER;

  /// 显示普通信息Toast
  static void showInfo(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: _defaultGravity,
      timeInSecForIosWeb: _defaultDuration.inSeconds,
      backgroundColor: CupertinoColors.black.withValues(alpha: 0.8),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 显示成功Toast
  static void showSuccess(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: _defaultGravity,
      timeInSecForIosWeb: _defaultDuration.inSeconds,
      backgroundColor: CupertinoColors.systemGreen.withValues(alpha: 0.9),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 显示错误Toast
  static void showError(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: _defaultGravity,
      timeInSecForIosWeb: _defaultDuration.inSeconds,
      backgroundColor: CupertinoColors.systemRed.withValues(alpha: 0.9),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 显示警告Toast
  static void showWarning(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: _defaultGravity,
      timeInSecForIosWeb: _defaultDuration.inSeconds,
      backgroundColor: CupertinoColors.systemOrange.withValues(alpha: 0.9),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 显示加载Toast（长时间显示）
  static void showLoading(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: _defaultGravity,
      timeInSecForIosWeb: 3,
      backgroundColor: CupertinoColors.systemBlue.withValues(alpha: 0.9),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 显示自定义Toast
  static void showCustom({
    required String message,
    Color? backgroundColor,
    Color? textColor,
    double? fontSize,
    ToastGravity? gravity,
    Toast? toastLength,
    int? timeInSecForIosWeb,
  }) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: toastLength ?? Toast.LENGTH_SHORT,
      gravity: gravity ?? _defaultGravity,
      timeInSecForIosWeb: timeInSecForIosWeb ?? _defaultDuration.inSeconds,
      backgroundColor: backgroundColor ?? CupertinoColors.black.withValues(alpha: 0.8),
      textColor: textColor ?? CupertinoColors.white,
      fontSize: fontSize ?? _defaultFontSize,
    );
  }

  /// 显示顶部Toast
  static void showTop(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: _defaultDuration.inSeconds,
      backgroundColor: CupertinoColors.black.withValues(alpha: 0.8),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 显示底部Toast
  static void showBottom(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: _defaultDuration.inSeconds,
      backgroundColor: CupertinoColors.black.withValues(alpha: 0.8),
      textColor: CupertinoColors.white,
      fontSize: _defaultFontSize,
    );
  }

  /// 取消当前显示的Toast
  static void cancel() {
    Fluttertoast.cancel();
  }

  /// 显示网络错误Toast
  static void showNetworkError([String? customMessage]) {
    showError(customMessage ?? '网络连接失败，请检查网络设置');
  }

  /// 显示服务器错误Toast
  static void showServerError([String? customMessage]) {
    showError(customMessage ?? '服务器错误，请稍后重试');
  }

  /// 显示权限错误Toast
  static void showPermissionError([String? customMessage]) {
    showWarning(customMessage ?? '权限不足，请检查相关权限设置');
  }

  /// 显示输入验证错误Toast
  static void showValidationError(String message) {
    showWarning(message);
  }

  /// 显示操作成功Toast
  static void showOperationSuccess([String? customMessage]) {
    showSuccess(customMessage ?? '操作成功');
  }

  /// 显示操作失败Toast
  static void showOperationFailed([String? customMessage]) {
    showError(customMessage ?? '操作失败，请重试');
  }

  /// 显示登录相关Toast
  static void showLoginSuccess() {
    showSuccess('登录成功');
  }

  static void showLoginFailed([String? reason]) {
    showError(reason ?? '登录失败，请检查用户名和密码');
  }

  static void showLogoutSuccess() {
    showInfo('已退出登录');
  }

  /// 显示数据加载Toast
  static void showDataLoading() {
    showLoading('正在加载数据...');
  }

  static void showDataLoadFailed() {
    showError('数据加载失败，请重试');
  }

  /// 显示保存相关Toast
  static void showSaveSuccess() {
    showSuccess('保存成功');
  }

  static void showSaveFailed() {
    showError('保存失败，请重试');
  }

  /// 显示删除相关Toast
  static void showDeleteSuccess() {
    showSuccess('删除成功');
  }

  static void showDeleteFailed() {
    showError('删除失败，请重试');
  }

  /// 显示复制成功Toast
  static void showCopySuccess() {
    showSuccess('已复制到剪贴板');
  }

  /// 显示分享成功Toast
  static void showShareSuccess() {
    showSuccess('分享成功');
  }

  /// 显示下载相关Toast
  static void showDownloadStart() {
    showLoading('开始下载...');
  }

  static void showDownloadSuccess() {
    showSuccess('下载完成');
  }

  static void showDownloadFailed() {
    showError('下载失败，请重试');
  }

  /// 显示上传相关Toast
  static void showUploadStart() {
    showLoading('正在上传...');
  }

  static void showUploadSuccess() {
    showSuccess('上传成功');
  }

  static void showUploadFailed() {
    showError('上传失败，请重试');
  }

  /// 显示同步相关Toast
  static void showSyncStart() {
    showLoading('正在同步...');
  }

  static void showSyncSuccess() {
    showSuccess('同步完成');
  }

  static void showSyncFailed() {
    showError('同步失败，请重试');
  }
}
