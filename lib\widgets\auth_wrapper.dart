import 'package:align/pages/main_page.dart';
import 'package:align/pages/setup_page.dart';
import 'package:align/services/auth_service.dart';
import 'package:flutter/cupertino.dart';

/// 认证包装器，根据登录状态显示不同的页面
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool? _isLoggedIn;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
    _listenToAuthChanges();
  }

  /// 检查初始登录状态
  Future<void> _checkLoginStatus() async {
    try {
      final isLoggedIn = await AuthService.instance.isLoggedIn();
      if (mounted) {
        setState(() {
          _isLoggedIn = isLoggedIn;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoggedIn = false;
          _isLoading = false;
        });
      }
    }
  }

  /// 监听认证状态变化
  void _listenToAuthChanges() {
    AuthService.instance.authStateStream.listen((isLoggedIn) {
      if (mounted) {
        setState(() {
          _isLoggedIn = isLoggedIn;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      // 显示加载界面
      return const CupertinoPageScaffold(
        child: Center(
          child: CupertinoActivityIndicator(),
        ),
      );
    }

    // 根据登录状态显示不同页面
    if (_isLoggedIn == true) {
      return const MainPage();
    } else {
      return const SetupPage();
    }
  }
}
