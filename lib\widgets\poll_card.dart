import 'package:align/utils/text_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PollCard extends StatelessWidget {
  const PollCard({
    super.key,
    required this.question,
    required this.options,
    required this.userImage,
    required this.userName,
  });

  final String question;
  final List<String> options;
  final String userImage;
  final String userName;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF1F1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('投票：', style: TextUtil.base.semiBold),
          SizedBox(height: 8.h),
          Text(
            question,
            style: TextUtil.base,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 12.h),
          _buildPollOptions(),
          SizedBox(height: 12.h),
          Row(
            children: [
              CircleAvatar(
                radius: 12.r,
                backgroundImage: AssetImage(userImage),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  userName,
                  style: TextUtil.base.sp(12).grey,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPollOptions() {
    return Column(
      children: options.asMap().entries.map((entry) {
        int idx = entry.key;
        String text = entry.value;
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          padding: EdgeInsets.symmetric(vertical: 10.h),
          decoration: BoxDecoration(
            color: const Color(0xFFFFDBDB),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Center(
            child: Text(
              '${idx + 1}. $text',
              style: TextUtil.base.semiBold.sp(14),
            ),
          ),
        );
      }).toList(),
    );
  }
} 