import 'package:align/utils/text_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PostCard extends StatelessWidget {
  const PostCard({
    super.key,
    required this.image,
    required this.title,
    required this.userImage,
    required this.userName,
    required this.imageHeight,
  });

  final String image;
  final String title;
  final String userImage;
  final String userName;
  final double imageHeight;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Image.asset(
            image,
            height: imageHeight.h,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          title,
          style: TextUtil.base.semiBold,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 8.h),
        Row(
          children: [
            CircleAvatar(
              radius: 12.r,
              backgroundImage: AssetImage(userImage),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                userName,
                style: TextUtil.base.sp(12).grey,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }
} 