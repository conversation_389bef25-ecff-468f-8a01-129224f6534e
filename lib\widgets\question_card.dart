import 'package:align/utils/text_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class QuestionCard extends StatelessWidget {
  const QuestionCard({
    super.key,
    required this.question,
    required this.userImage,
    required this.userName,
  });

  final String question;
  final String userImage;
  final String userName;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF1F1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('问题：', style: TextUtil.base.semiBold),
          SizedBox(height: 8.h),
          Text(
            question,
            style: TextUtil.base,
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
          Row(
            children: [
              CircleAvatar(
                radius: 12.r,
                backgroundImage: AssetImage(userImage),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  userName,
                  style: TextUtil.base.sp(12).grey,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 