import 'package:flutter_test/flutter_test.dart';
import 'package:align/services/auth_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('AuthService Tests', () {
    late AuthService authService;

    setUp(() async {
      // 在每个测试前清除SharedPreferences
      SharedPreferences.setMockInitialValues({});
      authService = AuthService.instance;
    });

    test('初始状态应该是未登录', () async {
      final isLoggedIn = await authService.isLoggedIn();
      expect(isLoggedIn, false);
    });

    test('登录功能应该正常工作', () async {
      // 执行登录
      final loginResult = await authService.login(
        username: 'test_user',
        password: 'test_password',
      );

      // 验证登录结果
      expect(loginResult, true);

      // 验证登录状态
      final isLoggedIn = await authService.isLoggedIn();
      expect(isLoggedIn, true);

      // 验证同步状态
      expect(authService.currentLoginStatus, true);
    });

    test('登录状态应该持久化保存', () async {
      // 执行登录
      await authService.login(
        username: 'test_user',
        password: 'test_password',
      );

      // 验证SharedPreferences中保存了登录状态
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getBool('is_logged_in'), true);

      // 重新检查登录状态（模拟应用重启）
      final isLoggedIn = await authService.isLoggedIn();
      expect(isLoggedIn, true);
    });

    test('登出功能应该正常工作', () async {
      // 先登录
      await authService.login(
        username: 'test_user',
        password: 'test_password',
      );

      // 验证已登录
      expect(authService.currentLoginStatus, true);

      // 执行登出
      await authService.logout();

      // 验证登出状态
      expect(authService.currentLoginStatus, false);
      final isLoggedIn = await authService.isLoggedIn();
      expect(isLoggedIn, false);

      // 验证SharedPreferences中的登录状态已清除
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getBool('is_logged_in'), null);
    });

    test('认证状态流应该正常工作', () async {
      // 监听状态变化
      final List<bool> stateChanges = [];
      final subscription = authService.authStateStream.listen((state) {
        stateChanges.add(state);
      });

      // 执行登录
      await authService.login(
        username: 'test_user',
        password: 'test_password',
      );

      // 执行登出
      await authService.logout();

      // 等待流事件处理
      await Future.delayed(const Duration(milliseconds: 100));

      // 验证状态变化
      expect(stateChanges.length, 2);
      expect(stateChanges[0], true);  // 登录状态
      expect(stateChanges[1], false); // 登出状态

      // 清理
      await subscription.cancel();
    });
  });
}
